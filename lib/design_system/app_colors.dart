import 'package:flutter/material.dart';

/// Design tokens for consistent colors throughout the app
class AppColors {
  // Primary brand colors (same for both themes)
  static const Color primary = Color(0xFF6366F1);
  static const Color primaryLight = Color(0xFF818CF8);
  static const Color primaryDark = Color(0xFF4F46E5);

  // Success, warning, error colors (same for both themes)
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);

  // Neutral colors
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color grey50 = Color(0xFFF9FAFB);
  static const Color grey100 = Color(0xFFF3F4F6);
  static const Color grey200 = Color(0xFFE5E7EB);
  static const Color grey300 = Color(0xFFD1D5DB);
  static const Color grey400 = Color(0xFF9CA3AF);
  static const Color grey500 = Color(0xFF6B7280);
  static const Color grey600 = Color(0xFF4B5563);
  static const Color grey700 = Color(0xFF374151);
  static const Color grey800 = Color(0xFF1F2937);
  static const Color grey900 = Color(0xFF111827);

  // Light theme colors
  static const Color lightBackground = Color(0xFFF8FAFC);
  static const Color lightCardBackground = white;
  static const Color lightSurface = white;
  static const Color lightTextPrimary = black;
  static const Color lightTextSecondary = grey500;
  static const Color lightBorder = grey200;
  static const Color lightBorderLight = grey100;

  // Dark theme colors - Enhanced for better contrast and hierarchy
  static const Color darkBackground = Color(0xFF0F0F0F);
  static const Color darkCardBackground = Color(0xFF1A1A1A);
  static const Color darkSurface = Color(0xFF1C1C1C);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB8B8B8);
  static const Color darkBorder = Color(0xFF2A2A2A);
  static const Color darkBorderLight = Color(0xFF333333);

  // Additional dark theme colors for enhanced hierarchy
  static const Color darkCardElevated = Color(0xFF222222);
  static const Color darkSurfaceVariant = Color(0xFF1C1C1C);
  static const Color darkTextTertiary = Color(0xFF909090);

  // Static colors that don't change between themes
  static const Color textOnPrimary = white;

  // Backwards compatibility - will be replaced with theme-aware versions
  static const Color background = lightBackground;
  static const Color cardBackground = lightCardBackground;
  static const Color surface = lightSurface;
  static const Color textPrimary = lightTextPrimary;
  static const Color textSecondary = lightTextSecondary;
  static const Color border = lightBorder;
  static const Color borderLight = lightBorderLight;
}

/// Extension to provide theme-aware colors
extension AppColorsTheme on AppColors {
  static Color background(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkBackground
        : AppColors.lightBackground;
  }

  static Color cardBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkCardBackground
        : AppColors.lightCardBackground;
  }

  static Color surface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkSurface
        : AppColors.lightSurface;
  }

  static Color textPrimary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkTextPrimary
        : AppColors.lightTextPrimary;
  }

  static Color textSecondary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkTextSecondary
        : AppColors.lightTextSecondary;
  }

  static Color border(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkBorder
        : AppColors.lightBorder;
  }

  static Color borderLight(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkBorderLight
        : AppColors.lightBorderLight;
  }

  static Color cardElevated(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkCardElevated
        : AppColors.lightCardBackground;
  }

  static Color surfaceVariant(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkSurfaceVariant
        : AppColors.lightSurface;
  }

  static Color textTertiary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.darkTextTertiary
        : AppColors.grey400;
  }
}