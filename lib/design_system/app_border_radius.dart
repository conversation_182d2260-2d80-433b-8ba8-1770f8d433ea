import 'package:flutter/material.dart';

/// Design tokens for consistent border radius throughout the app
class AppBorderRadius {
  // Base radius values
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 12.0;
  static const double lg = 16.0;
  static const double xl = 20.0;
  static const double xxl = 24.0;

  // Specific use cases
  static const double button = md; // 12px
  static const double card = lg; // 16px
  static const double modal = xl; // 20px
  static const double chip = sm; // 8px

  // BorderRadius objects for convenience
  static const BorderRadius buttonRadius = BorderRadius.all(Radius.circular(button));
  static const BorderRadius cardRadius = BorderRadius.all(Radius.circular(card));
  static const BorderRadius modalRadius = BorderRadius.all(Radius.circular(modal));
  static const BorderRadius chipRadius = BorderRadius.all(Radius.circular(chip));
}