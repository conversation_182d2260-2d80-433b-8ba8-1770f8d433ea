import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import 'app_scaffold.dart';

/// Specialized scaffold for authentication screens (login / signup).
/// Centred body, no bottom navigation, custom padding & scroll.
class AuthScaffold extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool canPop;

  const AuthScaffold({
    super.key,
    required this.child,
    this.title,
    this.canPop = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: title,
      canPop: canPop,
      showBottomNav: false,
      scrollable: true,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.screenPadding),
          child: child,
        ),
      ),
    );
  }
}