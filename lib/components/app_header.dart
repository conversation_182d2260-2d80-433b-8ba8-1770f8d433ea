import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// A standardized header component for screen titles
class AppHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? action;
  final VoidCallback? onBackPressed;
  final bool showBackButton;

  const AppHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.action,
    this.onBackPressed,
    this.showBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.screenPadding),
      child: Row(
        children: [
          if (showBackButton) ...[
            IconButton(
              onPressed: onBackPressed ?? () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back_ios, size: 20),
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : AppColors.grey100,
                foregroundColor: AppColorsTheme.textPrimary(context),
                padding: const EdgeInsets.all(AppSpacing.md),
                shape: RoundedRectangleBorder(
                  borderRadius: AppBorderRadius.buttonRadius,
                ),
              ),
            ),
            const SizedBox(width: AppSpacing.lg),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.display2.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: AppSpacing.textSpacing),
                  Text(
                    subtitle!,
                    style: AppTypography.body2.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (action != null) ...[
            const SizedBox(width: AppSpacing.lg),
            action!,
          ],
        ],
      ),
    );
  }
}

/// A standardized app bar header for workout screens
class AppWorkoutHeader extends StatelessWidget {
  final String? userName;
  final VoidCallback? onClose;
  final Widget? trailing;

  const AppWorkoutHeader({
    super.key,
    this.userName,
    this.onClose,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.screenPadding),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: AppBorderRadius.buttonRadius,
            ),
            child: const Icon(
              Icons.fitness_center,
              color: AppColors.textOnPrimary,
              size: 24,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'OpenFit',
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                  ),
                ),
                Text(
                  'by OpenThrive',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ),
          if (trailing != null) ...[
            trailing!,
          ] else if (onClose != null) ...[
            GestureDetector(
              onTap: onClose,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.darkBorder
                      : AppColors.grey100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: AppColorsTheme.textSecondary(context),
                  size: 20,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}