import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import 'app_bottom_navigation.dart';
import 'app_app_bar.dart';

/// The single source of truth for screen layout.
///
/// * Injects background colours from the design system.
/// * Provides an [AppAppBar] and optional [AppBottomNavigation].
/// * Offers safe-area & scrollable body conveniences so screens stay DRY.
class AppScaffold extends StatelessWidget {
  final String? title;
  final Widget body;
  final List<Widget>? actions;
  final bool canPop;
  final bool useSafeArea;
  final bool scrollable;
  final bool showBottomNav;
  final AppNavigationItem bottomNavItem;

  const AppScaffold({
    super.key,
    required this.body,
    this.title,
    this.actions,
    this.canPop = false,
    this.useSafeArea = true,
    this.scrollable = true,
    this.showBottomNav = false,
    this.bottomNavItem = AppNavigationItem.home,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = body;
    if (scrollable) {
      content = SingleChildScrollView(child: content);
    }
    if (useSafeArea) {
      content = SafeArea(child: content);
    }

    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      appBar: AppAppBar(
        title: title,
        actions: actions,
        canPop: canPop,
      ),
      body: content,
      bottomNavigationBar: showBottomNav
          ? AppBottomNavigation(
              currentItem: bottomNavItem,
              onItemTapped: (item) => navigateToItem(context, item),
            )
          : null,
    );
  }
}