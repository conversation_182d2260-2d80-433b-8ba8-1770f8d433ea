import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthService extends ChangeNotifier {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  User? get currentUser => _supabase.auth.currentUser;
  Session? get currentSession => _supabase.auth.currentSession;
  bool get isAuthenticated => currentUser != null;

  AuthService() {
    _supabase.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;
      
      if (event == AuthChangeEvent.signedIn && session != null) {
        debugPrint('User signed in: ${session.user.email}');
      } else if (event == AuthChangeEvent.signedOut) {
        debugPrint('User signed out');
      }
      
      notifyListeners();
    });
  }

  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: displayName != null ? {'display_name': displayName} : null,
      );

      if (response.user != null) {
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          displayName: displayName,
        );
      }

      return response;
    } on AuthException catch (error) {
      throw Exception(error.message);
    } catch (error) {
      throw Exception('An unexpected error occurred: $error');
    }
  }

  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      return response;
    } on AuthException catch (error) {
      throw Exception(error.message);
    } catch (error) {
      throw Exception('An unexpected error occurred: $error');
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } on AuthException catch (error) {
      throw Exception(error.message);
    } catch (error) {
      throw Exception('An unexpected error occurred: $error');
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } on AuthException catch (error) {
      throw Exception(error.message);
    } catch (error) {
      throw Exception('An unexpected error occurred: $error');
    }
  }

  Future<void> _createUserProfile({
    required String userId,
    required String email,
    String? displayName,
  }) async {
    try {
      await _supabase.from('profiles').insert({
        'id': userId,
        'email': email,
        'display_name': displayName,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'onboarding_completed': false,
        'has_completed_preferences': false,
        'fitness_assessment_completed': false,
      });
    } catch (error) {
      debugPrint('Error creating user profile: $error');
    }
  }

  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      if (currentUser == null) return null;
      
      final data = await _supabase
          .from('profiles')
          .select()
          .eq('id', currentUser!.id)
          .single();
      
      return data;
    } catch (error) {
      debugPrint('Error fetching user profile: $error');
      return null;
    }
  }

  Future<void> updateUserProfile(Map<String, dynamic> updates) async {
    try {
      if (currentUser == null) return;
      
      await _supabase
          .from('profiles')
          .update(updates)
          .eq('id', currentUser!.id);
          
      notifyListeners();
    } catch (error) {
      throw Exception('Failed to update profile: $error');
    }
  }
}