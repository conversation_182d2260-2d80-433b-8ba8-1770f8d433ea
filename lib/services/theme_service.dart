import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enum for theme modes
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Service for managing theme preferences and system theme detection
class ThemeService {
  static const String _themeKey = 'app_theme_mode';
  
  /// Get the saved theme mode from SharedPreferences
  static Future<AppThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeString = prefs.getString(_themeKey);
    
    switch (themeString) {
      case 'light':
        return AppThemeMode.light;
      case 'dark':
        return AppThemeMode.dark;
      case 'system':
      default:
        return AppThemeMode.system;
    }
  }
  
  /// Save the theme mode to SharedPreferences
  static Future<void> setThemeMode(AppThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, themeMode.name);
  }
  
  /// Get the actual theme mode based on system settings
  static ThemeMode getActualThemeMode(AppThemeMode appThemeMode, BuildContext context) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
  
  /// Check if the current theme is dark
  static bool isDarkMode(BuildContext context, AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }
}
