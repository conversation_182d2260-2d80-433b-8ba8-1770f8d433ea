import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/workout.dart';
import '../models/exercise.dart';

class WorkoutService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get all workouts with exercises for the current user
  Future<List<Workout>> getUserWorkouts() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Fetch workouts with exercise count
      final response = await _supabase
          .from('workouts')
          .select('''
            *,
            workout_exercises!inner(
              id,
              exercise_id,
              sets,
              reps,
              weight,
              order_index,
              rest_interval,
              name,
              completed,
              exercises!inner(
                id,
                name,
                description,
                video_url,
                primary_muscle,
                secondary_muscle,
                equipment,
                category,
                instructions,
                vertical_video
              )
            )
          ''')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      final List<Workout> workouts = [];
      
      for (final workoutData in response) {
        // Process workout exercises
        final List<WorkoutExercise> exercises = [];
        if (workoutData['workout_exercises'] != null) {
          for (final exerciseData in workoutData['workout_exercises']) {
            // Create Exercise object
            final exercise = Exercise.fromJson(exerciseData['exercises']);
            
            // Create WorkoutExercise object
            final workoutExercise = WorkoutExercise(
              id: exerciseData['id'],
              workoutId: workoutData['id'],
              exerciseId: exerciseData['exercise_id'],
              exercise: exercise,
              sets: exerciseData['sets'] ?? 0,
              reps: exerciseData['reps'] != null 
                  ? List<int>.from(exerciseData['reps']) 
                  : [],
              weight: exerciseData['weight'] != null 
                  ? List<int>.from(exerciseData['weight']) 
                  : null,
              orderIndex: exerciseData['order_index'] ?? 0,
              restInterval: exerciseData['rest_interval'],
              name: exerciseData['name'] ?? exercise.name,
              completed: exerciseData['completed'] ?? false,
            );
            
            exercises.add(workoutExercise);
          }
        }

        // Sort exercises by order_index
        exercises.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));

        // Create Workout object
        final workout = Workout(
          id: workoutData['id'],
          userId: workoutData['user_id'],
          name: workoutData['name'],
          aiDescription: workoutData['ai_description'],
          startTime: workoutData['start_time'] != null 
              ? DateTime.parse(workoutData['start_time']) 
              : null,
          endTime: workoutData['end_time'] != null 
              ? DateTime.parse(workoutData['end_time']) 
              : null,
          isActive: workoutData['is_active'] ?? false,
          duration: workoutData['duration'],
          notes: workoutData['notes'],
          rating: workoutData['rating'],
          isMinimized: workoutData['is_minimized'] ?? false,
          lastState: workoutData['last_state'],
          isCompleted: workoutData['is_completed'],
          sessionOrder: workoutData['session_order'],
          createdAt: DateTime.parse(workoutData['created_at']),
          updatedAt: workoutData['updated_at'] != null 
              ? DateTime.parse(workoutData['updated_at']) 
              : null,
          exercises: exercises,
        );

        workouts.add(workout);
      }

      return workouts;
    } catch (e) {
      print('Error fetching workouts: $e');
      rethrow;
    }
  }

  // Get all available workouts (including those without user_id for templates)
  Future<List<Workout>> getAllAvailableWorkouts() async {
    try {
      final user = _supabase.auth.currentUser;
      
      // Fetch workouts with exercises that have at least one exercise
      final response = await _supabase
          .from('workouts')
          .select('''
            *,
            workout_exercises!inner(
              id,
              exercise_id,
              sets,
              reps,
              weight,
              order_index,
              rest_interval,
              name,
              completed,
              exercises!inner(
                id,
                name,
                description,
                video_url,
                primary_muscle,
                secondary_muscle,
                equipment,
                category,
                instructions,
                vertical_video
              )
            )
          ''')
          .order('created_at', ascending: false);

      final List<Workout> workouts = [];
      
      for (final workoutData in response) {
        // Skip if this is a user-specific workout and not the current user's
        if (workoutData['user_id'] != null && 
            user != null && 
            workoutData['user_id'] != user.id) {
          continue;
        }

        // Process workout exercises
        final List<WorkoutExercise> exercises = [];
        if (workoutData['workout_exercises'] != null) {
          for (final exerciseData in workoutData['workout_exercises']) {
            // Create Exercise object
            final exercise = Exercise.fromJson(exerciseData['exercises']);
            
            // Create WorkoutExercise object
            final workoutExercise = WorkoutExercise(
              id: exerciseData['id'],
              workoutId: workoutData['id'],
              exerciseId: exerciseData['exercise_id'],
              exercise: exercise,
              sets: exerciseData['sets'] ?? 0,
              reps: exerciseData['reps'] != null 
                  ? List<int>.from(exerciseData['reps']) 
                  : [],
              weight: exerciseData['weight'] != null 
                  ? List<int>.from(exerciseData['weight']) 
                  : null,
              orderIndex: exerciseData['order_index'] ?? 0,
              restInterval: exerciseData['rest_interval'],
              name: exerciseData['name'] ?? exercise.name,
              completed: exerciseData['completed'] ?? false,
            );
            
            exercises.add(workoutExercise);
          }
        }

        // Only include workouts that have exercises
        if (exercises.isNotEmpty) {
          // Sort exercises by order_index
          exercises.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));

          // Create Workout object
          final workout = Workout(
            id: workoutData['id'],
            userId: workoutData['user_id'] ?? '',
            name: workoutData['name'],
            aiDescription: workoutData['ai_description'],
            startTime: workoutData['start_time'] != null 
                ? DateTime.parse(workoutData['start_time']) 
                : null,
            endTime: workoutData['end_time'] != null 
                ? DateTime.parse(workoutData['end_time']) 
                : null,
            isActive: workoutData['is_active'] ?? false,
            duration: workoutData['duration'],
            notes: workoutData['notes'],
            rating: workoutData['rating'],
            isMinimized: workoutData['is_minimized'] ?? false,
            lastState: workoutData['last_state'],
            isCompleted: workoutData['is_completed'],
            sessionOrder: workoutData['session_order'],
            createdAt: DateTime.parse(workoutData['created_at']),
            updatedAt: workoutData['updated_at'] != null 
                ? DateTime.parse(workoutData['updated_at']) 
                : null,
            exercises: exercises,
          );

          workouts.add(workout);
        }
      }

      return workouts;
    } catch (e) {
      print('Error fetching available workouts: $e');
      rethrow;
    }
  }

  // Get a specific workout with exercises
  Future<Workout?> getWorkout(String workoutId) async {
    try {
      final response = await _supabase
          .from('workouts')
          .select('''
            *,
            workout_exercises(
              id,
              exercise_id,
              sets,
              reps,
              weight,
              order_index,
              rest_interval,
              name,
              completed,
              exercises(
                id,
                name,
                description,
                video_url,
                primary_muscle,
                secondary_muscle,
                equipment,
                category,
                instructions,
                vertical_video
              )
            )
          ''')
          .eq('id', workoutId)
          .single();

      // Process workout exercises
      final List<WorkoutExercise> exercises = [];
      if (response['workout_exercises'] != null) {
        for (final exerciseData in response['workout_exercises']) {
          // Create Exercise object
          final exercise = Exercise.fromJson(exerciseData['exercises']);
          
          // Create WorkoutExercise object
          final workoutExercise = WorkoutExercise(
            id: exerciseData['id'],
            workoutId: response['id'],
            exerciseId: exerciseData['exercise_id'],
            exercise: exercise,
            sets: exerciseData['sets'] ?? 0,
            reps: exerciseData['reps'] != null 
                ? List<int>.from(exerciseData['reps']) 
                : [],
            weight: exerciseData['weight'] != null 
                ? List<int>.from(exerciseData['weight']) 
                : null,
            orderIndex: exerciseData['order_index'] ?? 0,
            restInterval: exerciseData['rest_interval'],
            name: exerciseData['name'] ?? exercise.name,
            completed: exerciseData['completed'] ?? false,
          );
          
          exercises.add(workoutExercise);
        }
      }

      // Sort exercises by order_index
      exercises.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));

      // Create Workout object
      final workout = Workout(
        id: response['id'],
        userId: response['user_id'] ?? '',
        name: response['name'],
        aiDescription: response['ai_description'],
        startTime: response['start_time'] != null 
            ? DateTime.parse(response['start_time']) 
            : null,
        endTime: response['end_time'] != null 
            ? DateTime.parse(response['end_time']) 
            : null,
        isActive: response['is_active'] ?? false,
        duration: response['duration'],
        notes: response['notes'],
        rating: response['rating'],
        isMinimized: response['is_minimized'] ?? false,
        lastState: response['last_state'],
        isCompleted: response['is_completed'],
        sessionOrder: response['session_order'],
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: response['updated_at'] != null 
            ? DateTime.parse(response['updated_at']) 
            : null,
        exercises: exercises,
      );

      return workout;
    } catch (e) {
      print('Error fetching workout: $e');
      return null;
    }
  }

  // Start a workout session
  Future<void> startWorkout(String workoutId) async {
    try {
      await _supabase
          .from('workouts')
          .update({
            'is_active': true,
            'start_time': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', workoutId);
    } catch (e) {
      print('Error starting workout: $e');
      rethrow;
    }
  }

  // Complete a workout session
  Future<void> completeWorkout(String workoutId) async {
    try {
      await _supabase
          .from('workouts')
          .update({
            'is_active': false,
            'is_completed': true,
            'end_time': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', workoutId);
    } catch (e) {
      print('Error completing workout: $e');
      rethrow;
    }
  }
}