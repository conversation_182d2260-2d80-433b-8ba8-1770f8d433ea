import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/onboarding_data.dart';

class ProfileService extends ChangeNotifier {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  String? get currentUserId => _supabase.auth.currentUser?.id;

  /// Save onboarding data to the user's profile
  Future<void> saveOnboardingData(OnboardingData onboardingData) async {
    try {
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      final profileData = onboardingData.toJson();
      
      // Update the user's profile with onboarding data
      await _supabase
          .from('profiles')
          .update(profileData)
          .eq('id', currentUserId!);
          
      debugPrint('Onboarding data saved successfully');
      notifyListeners();
    } catch (error) {
      debugPrint('Error saving onboarding data: $error');
      throw Exception('Failed to save onboarding data: $error');
    }
  }

  /// Get user profile data
  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      if (currentUserId == null) return null;
      
      final data = await _supabase
          .from('profiles')
          .select()
          .eq('id', currentUserId!)
          .maybeSingle();
      
      return data;
    } catch (error) {
      debugPrint('Error fetching user profile: $error');
      return null;
    }
  }

  /// Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding() async {
    try {
      final profile = await getUserProfile();
      return profile?['onboarding_completed'] == true;
    } catch (error) {
      debugPrint('Error checking onboarding status: $error');
      return false;
    }
  }

  /// Update specific profile fields
  Future<void> updateProfile(Map<String, dynamic> updates) async {
    try {
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }
      
      // Add updated_at timestamp
      updates['updated_at'] = DateTime.now().toIso8601String();
      
      await _supabase
          .from('profiles')
          .update(updates)
          .eq('id', currentUserId!);
          
      notifyListeners();
    } catch (error) {
      throw Exception('Failed to update profile: $error');
    }
  }

  /// Convert database profile data back to OnboardingData model
  OnboardingData? profileToOnboardingData(Map<String, dynamic>? profile) {
    if (profile == null) return null;
    
    try {
      // Helper function to safely convert values
      String? safeString(dynamic value) {
        if (value == null) return null;
        return value.toString();
      }
      
      int? safeInt(dynamic value) {
        if (value == null) return null;
        if (value is int) return value;
        if (value is String) return int.tryParse(value);
        return null;
      }
      
      double? safeDouble(dynamic value) {
        if (value == null) return null;
        double? result;
        if (value is double) {
          result = value;
        } else if (value is int) {
          result = value.toDouble();
        } else if (value is String) {
          result = double.tryParse(value);
        }
        
        // Validate reasonable ranges for height (50-300 cm) and weight (20-500 kg)
        if (result != null) {
          if (result > 1000) {
            return null; // Clearly invalid data
          }
        }
        return result;
      }
      
      List<String> safeStringList(dynamic value) {
        if (value == null) return [];
        if (value is List) {
          return value.map((e) => e.toString()).toList();
        }
        return [];
      }
      
      debugPrint('Converting profile data - available keys: ${profile.keys.toList()}');
      
      // Try to get display name, or extract from email
      String? displayName = safeString(profile['display_name']) ?? safeString(profile['name']);
      if (displayName == null || displayName.isEmpty) {
        // Extract name from email if no display name
        final email = safeString(profile['email']);
        if (email != null) {
          displayName = email.split('@').first;
        }
      }
      
      return OnboardingData(
        name: displayName,
        gender: safeString(profile['gender']),
        age: safeInt(profile['age']),
        height: safeDouble(profile['height']),
        heightUnit: safeString(profile['height_unit']) ?? 'cm',
        weight: safeDouble(profile['weight']),
        weightUnit: safeString(profile['weight_unit']) ?? 'kg',
        // Try multiple possible column names for primary goal
        primaryGoal: safeString(profile['fitness_goal_primary']) ?? 
                    safeString(profile['fitness_goal']) ?? 
                    safeString(profile['primarygoal']),
        // Try multiple possible column names for fitness goals array
        fitnessGoals: safeStringList(profile['fitness_goals_array']).isNotEmpty ? 
                     safeStringList(profile['fitness_goals_array']) : 
                     safeStringList(profile['fitnessgoals']),
        fitnessExperience: safeString(profile['fitness_experience']) ?? 
                          safeString(profile['training_experience_level']),
        // Try multiple possible column names for cardio level
        cardioLevel: safeInt(profile['cardio_fitness_level']) ?? 
                    safeInt(profile['cardiolevel']),
        // Try multiple possible column names for weightlifting level
        weightliftingLevel: safeInt(profile['weightlifting_fitness_level']) ?? 
                           safeInt(profile['weightliftinglevel']),
        workoutFrequency: safeInt(profile['workout_frequency']) ?? 
                         safeInt(profile['workoutfrequency']),
        workoutDuration: safeString(profile['workout_duration']) ?? 
                        safeString(profile['workoutduration']) ?? 
                        safeString(profile['workout_duration_preference']),
        workoutDays: safeStringList(profile['workout_days']).isNotEmpty ? 
                    safeStringList(profile['workout_days']) : 
                    safeStringList(profile['workoutdays']),
        workoutEnvironment: safeStringList(profile['workout_environment']),
        equipment: safeStringList(profile['equipment']),
        healthConditions: safeStringList(profile['health_conditions']),
        physicalLimitations: safeStringList(profile['physical_limitations']),
        dietaryRestrictions: safeStringList(profile['dietary_restrictions']) .isNotEmpty ? 
                           safeStringList(profile['dietary_restrictions']) : 
                           safeStringList(profile['diet_preferences']),
        additionalNotes: safeString(profile['additional_notes']) ?? 
                        safeString(profile['additional_health_info']),
      );
    } catch (error) {
      debugPrint('Error converting profile to onboarding data: $error');
      debugPrint('Profile data: $profile');
      return null;
    }
  }

  /// Create or update fitness assessment data
  Future<void> saveFitnessAssessment(Map<String, dynamic> assessmentData) async {
    try {
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      await _supabase
          .from('fitness_assessments')
          .upsert({
            'user_id': currentUserId!,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
            ...assessmentData,
          });

      // Mark assessment as completed in profile
      await updateProfile({
        'fitness_assessment_completed': true,
      });
      
    } catch (error) {
      throw Exception('Failed to save fitness assessment: $error');
    }
  }

  /// Get user's fitness goals for dashboard/recommendations
  Future<List<String>> getUserFitnessGoals() async {
    try {
      final profile = await getUserProfile();
      if (profile == null) return [];
      
      final primaryGoal = profile['fitness_goal_primary'] as String?;
      final goalsArray = List<String>.from(profile['fitness_goals_array'] ?? []);
      
      Set<String> allGoals = {};
      if (primaryGoal != null) allGoals.add(primaryGoal);
      allGoals.addAll(goalsArray);
      
      return allGoals.toList();
    } catch (error) {
      debugPrint('Error fetching fitness goals: $error');
      return [];
    }
  }

  /// Get user's available equipment for workout filtering
  Future<List<String>> getUserEquipment() async {
    try {
      final profile = await getUserProfile();
      if (profile == null) return [];
      
      return List<String>.from(profile['equipment'] ?? []);
    } catch (error) {
      debugPrint('Error fetching equipment: $error');
      return [];
    }
  }

  /// Get user's workout preferences
  Future<Map<String, dynamic>> getWorkoutPreferences() async {
    try {
      final profile = await getUserProfile();
      if (profile == null) return {};
      
      return {
        'frequency': profile['workout_frequency'],
        'duration': profile['workout_duration'],
        'days': List<String>.from(profile['workout_days'] ?? []),
        'environment': List<String>.from(profile['workout_environment'] ?? []),
        'experience': profile['fitness_experience'],
        'cardio_level': profile['cardio_fitness_level'],
        'weightlifting_level': profile['weightlifting_fitness_level'],
      };
    } catch (error) {
      debugPrint('Error fetching workout preferences: $error');
      return {};
    }
  }
}