import 'package:flutter/material.dart';

class SetFeedbackModal extends StatefulWidget {
  final int completedReps;
  final int completedWeight;
  final String exerciseName;
  final int currentSet;
  final int totalSets;
  final VoidCallback onComplete;
  final Function(String difficulty, bool personalizeWeight)? onFeedbackSubmitted;

  const SetFeedbackModal({
    super.key,
    required this.completedReps,
    required this.completedWeight,
    required this.exerciseName,
    required this.currentSet,
    required this.totalSets,
    required this.onComplete,
    this.onFeedbackSubmitted,
  });

  @override
  State<SetFeedbackModal> createState() => _SetFeedbackModalState();
}

class _SetFeedbackModalState extends State<SetFeedbackModal>
    with SingleTickerProviderStateMixin {
  String? _selectedDifficulty;
  bool _isSubmitting = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<String> _difficultyLevels = [
    'Effortless',
    'Easy', 
    'Ideal',
    'Hard'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Stack(
          children: [
            // Semi-transparent overlay
            FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                color: Colors.black.withValues(alpha: 0.6),
              ),
            ),
            // Modal content
            SlideTransition(
              position: _slideAnimation,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildHeader(),
                      _buildDifficultyRating(),
                      _buildAIFeedback(),
                      _buildActionButtons(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const Text(
            'How difficult was that set?',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Rating your effort level helps me optimize\nweights I recommend for you.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyRating() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: _difficultyLevels.map((difficulty) {
          final isSelected = _selectedDifficulty == difficulty;
          final isIdeal = difficulty == 'Ideal';
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDifficulty = difficulty;
              });
            },
            child: Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected 
                    ? (isIdeal ? const Color(0xFF6366F1) : Colors.grey[300])
                    : Colors.grey[100],
                border: Border.all(
                  color: isSelected 
                      ? (isIdeal ? const Color(0xFF6366F1) : Colors.grey[400]!)
                      : Colors.grey[300]!,
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isSelected && isIdeal && _isSubmitting)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  else
                    Icon(
                      _getDifficultyIcon(difficulty),
                      color: isSelected 
                          ? (isIdeal ? Colors.white : Colors.grey[700])
                          : Colors.grey[500],
                      size: 24,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    difficulty,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: isSelected 
                          ? (isIdeal ? Colors.white : Colors.grey[700])
                          : Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAIFeedback() {
    if (_selectedDifficulty == null) return const SizedBox.shrink();
    
    String feedbackText = _getAIFeedback(_selectedDifficulty!);
    
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF6366F1).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF6366F1).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feedbackText,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF6366F1),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final showButtons = _selectedDifficulty != null;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: showButtons ? 120 : 0,
      child: showButtons 
          ? SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : () => _submitFeedback(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6366F1),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.tune, size: 16),
                            const SizedBox(width: 6),
                            Text(
                              _isSubmitting ? 'Processing...' : 'Personalize Your Weight',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: double.infinity,
                      height: 44,
                      child: TextButton(
                        onPressed: _isSubmitting ? null : () => _submitFeedback(false),
                        style: TextButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Skip Personalization',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  IconData _getDifficultyIcon(String difficulty) {
    switch (difficulty) {
      case 'Effortless':
        return Icons.sentiment_very_satisfied;
      case 'Easy':
        return Icons.sentiment_satisfied;
      case 'Ideal':
        return Icons.favorite;
      case 'Hard':
        return Icons.sentiment_dissatisfied;
      default:
        return Icons.circle;
    }
  }

  String _getAIFeedback(String difficulty) {
    switch (difficulty) {
      case 'Effortless':
        return 'You could do 8-10 more reps';
      case 'Easy':
        return 'You could do 2-5 more reps';
      case 'Ideal':
        return 'Perfect! This weight is ideal for your training';
      case 'Hard':
        return 'Consider reducing weight by 5-10 lbs';
      default:
        return 'Keep up the great work!';
    }
  }

  void _submitFeedback(bool personalizeWeight) async {
    if (_selectedDifficulty == null) return;
    
    setState(() {
      _isSubmitting = true;
    });

    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 800));

    if (widget.onFeedbackSubmitted != null) {
      widget.onFeedbackSubmitted!(_selectedDifficulty!, personalizeWeight);
    }

    // Animate out
    await _animationController.reverse();
    
    widget.onComplete();
  }
}