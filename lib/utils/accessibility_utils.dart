import 'dart:math';
import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Utility class for accessibility enhancements
class AccessibilityUtils {
  /// Check if color contrast meets WCAG AA standards (4.5:1 for normal text)
  static bool meetsContrastRequirement(Color foreground, Color background, {bool isLargeText = false}) {
    final ratio = calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText ? 3.0 : 4.5;
    return ratio >= requiredRatio;
  }

  /// Calculate contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent((color.r * 255.0).round() / 255.0);
    final g = _linearizeColorComponent((color.g * 255.0).round() / 255.0);
    final b = _linearizeColorComponent((color.b * 255.0).round() / 255.0);

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Get accessible text color for given background
  static Color getAccessibleTextColor(Color background, BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final primaryText = isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final secondaryText = isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;
    
    if (meetsContrastRequirement(primaryText, background)) {
      return primaryText;
    } else if (meetsContrastRequirement(secondaryText, background)) {
      return secondaryText;
    } else {
      // Fallback to high contrast
      return isDark ? Colors.white : Colors.black;
    }
  }

  /// Enhanced semantic labels for screen readers
  static String getWorkoutStatusSemantics(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active workout, ready to continue';
      case 'completed':
        return 'Completed workout';
      case 'draft':
        return 'Draft workout, not yet started';
      default:
        return 'Workout status: $status';
    }
  }

  /// Get semantic label for progress stats
  static String getProgressSemantics(String label, String value, String? subtitle) {
    final base = '$label: $value';
    return subtitle != null ? '$base $subtitle' : base;
  }

  /// Enhanced button semantics
  static String getButtonSemantics(String text, {String? context, bool isLoading = false}) {
    if (isLoading) {
      return '$text, loading';
    }
    return context != null ? '$text, $context' : text;
  }
}

/// Extension for adding accessibility features to widgets
extension AccessibilityExtension on Widget {
  /// Add semantic label and improved accessibility
  Widget withAccessibility({
    required String label,
    String? hint,
    bool isButton = false,
    bool isHeader = false,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: isButton,
      header: isHeader,
      onTap: onTap,
      child: this,
    );
  }

  /// Add focus and hover effects for better accessibility
  Widget withFocusEffects(BuildContext context) {
    return Focus(
      child: Builder(
        builder: (context) {
          final hasFocus = Focus.of(context).hasFocus;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              border: hasFocus ? Border.all(
                color: AppColors.primary,
                width: 2,
              ) : null,
              borderRadius: AppBorderRadius.cardRadius,
            ),
            child: this,
          );
        },
      ),
    );
  }
}

/// WCAG compliant color validator
class WCAGColorValidator {
  /// Validate all app colors for WCAG compliance
  static Map<String, bool> validateAppColors(BuildContext context) {
    final results = <String, bool>{};

    // Test primary text on background
    results['Primary text on background'] = AccessibilityUtils.meetsContrastRequirement(
      AppColorsTheme.textPrimary(context),
      AppColorsTheme.background(context),
    );

    // Test secondary text on background
    results['Secondary text on background'] = AccessibilityUtils.meetsContrastRequirement(
      AppColorsTheme.textSecondary(context),
      AppColorsTheme.background(context),
    );

    // Test text on cards
    results['Primary text on cards'] = AccessibilityUtils.meetsContrastRequirement(
      AppColorsTheme.textPrimary(context),
      AppColorsTheme.cardBackground(context),
    );

    // Test button text
    results['Button text contrast'] = AccessibilityUtils.meetsContrastRequirement(
      AppColors.textOnPrimary,
      AppColors.primary,
    );

    // Test status indicators
    results['Success status contrast'] = AccessibilityUtils.meetsContrastRequirement(
      AppColors.success,
      AppColorsTheme.cardBackground(context),
    );

    results['Error status contrast'] = AccessibilityUtils.meetsContrastRequirement(
      AppColors.error,
      AppColorsTheme.cardBackground(context),
    );

    return results;
  }

  /// Get recommendations for improving accessibility
  static List<String> getAccessibilityRecommendations(BuildContext context) {
    final recommendations = <String>[];
    final results = validateAppColors(context);

    results.forEach((test, passed) {
      if (!passed) {
        recommendations.add('Improve contrast for: $test');
      }
    });

    if (recommendations.isEmpty) {
      recommendations.add('All color contrasts meet WCAG AA standards!');
    }

    return recommendations;
  }
}
