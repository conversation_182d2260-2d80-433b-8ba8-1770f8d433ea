import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import 'accessibility_utils.dart';

/// Test widget to validate accessibility and contrast in both themes
class AccessibilityTestWidget extends StatelessWidget {
  const AccessibilityTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final results = WCAGColorValidator.validateAppColors(context);
    final recommendations = WCAGColorValidator.getAccessibilityRecommendations(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      appBar: AppBar(
        title: Text('Accessibility Test - ${isDark ? 'Dark' : 'Light'} Mode'),
        backgroundColor: AppColorsTheme.surface(context),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Color Contrast Results
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'WCAG Color Contrast Results',
                    style: AppTypography.heading2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  const SizedBox(height: AppSpacing.md),
                  ...results.entries.map((entry) => Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                    child: Row(
                      children: [
                        Icon(
                          entry.value ? Icons.check_circle : Icons.error,
                          color: entry.value ? AppColors.success : AppColors.error,
                          size: 20,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Text(
                            entry.key,
                            style: AppTypography.body1.copyWith(
                              color: AppColorsTheme.textPrimary(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // Recommendations
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Accessibility Recommendations',
                    style: AppTypography.heading2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  const SizedBox(height: AppSpacing.md),
                  ...recommendations.map((recommendation) => Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.lightbulb_outline,
                          color: AppColors.warning,
                          size: 20,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Text(
                            recommendation,
                            style: AppTypography.body1.copyWith(
                              color: AppColorsTheme.textPrimary(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // Sample UI Elements for Visual Testing
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'UI Elements Test',
                    style: AppTypography.heading2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  const SizedBox(height: AppSpacing.md),
                  
                  // Status badges
                  Wrap(
                    spacing: AppSpacing.sm,
                    runSpacing: AppSpacing.sm,
                    children: [
                      _buildStatusBadge(context, 'Active', AppColors.success),
                      _buildStatusBadge(context, 'Draft', AppColors.grey500),
                      _buildStatusBadge(context, 'Completed', AppColors.primary),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Buttons
                  Row(
                    children: [
                      Expanded(
                        child: AppButton(
                          text: 'Primary Button',
                          onPressed: () {},
                          variant: AppButtonVariant.primary,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: AppButton(
                          text: 'Secondary',
                          onPressed: () {},
                          variant: AppButtonVariant.secondary,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  AppButton(
                    text: 'Outline Button',
                    onPressed: () {},
                    variant: AppButtonVariant.outline,
                    fullWidth: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context, String text, Color color) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: isDark 
            ? color.withValues(alpha: 0.2)
            : color.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.chipRadius,
        border: Border.all(
          color: isDark 
              ? color.withValues(alpha: 0.6)
              : color.withValues(alpha: 0.3),
          width: isDark ? 1.5 : 1,
        ),
      ),
      child: Text(
        text,
        style: AppTypography.small.copyWith(
          fontWeight: AppTypography.semiBold,
          color: isDark ? color.withValues(alpha: 0.9) : color,
        ),
      ),
    );
  }
}
