import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/onboarding_data.dart';
import '../../../design_system/design_system.dart';

class PersonalInfoStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const PersonalInfoStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _updateControllers();
  }

  @override
  void didUpdateWidget(PersonalInfoStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateControllers();
  }

  void _updateControllers() {
    _nameController.text = widget.onboardingData.name ?? '';
    _ageController.text = widget.onboardingData.age?.toString() ?? '';
    _heightController.text = widget.onboardingData.height?.toString() ?? '';
    _weightController.text = widget.onboardingData.weight?.toString() ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _updateData() {
    widget.onboardingData.name = _nameController.text;
    widget.onboardingData.age = int.tryParse(_ageController.text);
    widget.onboardingData.height = double.tryParse(_heightController.text);
    widget.onboardingData.weight = double.tryParse(_weightController.text);
    widget.onDataChanged();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Full Name
          Text(
            'Full Name*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColorsTheme.borderLight(context)),
            ),
            child: TextFormField(
              controller: _nameController,
              style: TextStyle(color: AppColorsTheme.textPrimary(context)),
              onChanged: (_) => _updateData(),
              decoration: InputDecoration(
                hintText: 'Enter your full name',
                hintStyle: TextStyle(color: AppColorsTheme.textSecondary(context)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
          ),
                
          const SizedBox(height: 24),
          
          // Gender Selection
          Text(
            'Gender*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColorsTheme.borderLight(context)),
            ),
            child: DropdownButtonFormField<String>(
              value: widget.onboardingData.gender,
              decoration: InputDecoration(
                hintText: 'Select your gender',
                hintStyle: TextStyle(color: AppColorsTheme.textSecondary(context)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              dropdownColor: AppColorsTheme.cardBackground(context),
              style: TextStyle(color: AppColorsTheme.textPrimary(context)),
              items: OnboardingConstants.genders.map((gender) {
                return DropdownMenuItem(
                  value: gender,
                  child: Text(
                    gender,
                    style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  widget.onboardingData.gender = value;
                });
                _updateData();
              },
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Age Field
          Text(
            'Age*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColorsTheme.borderLight(context)),
            ),
            child: TextFormField(
              controller: _ageController,
              keyboardType: TextInputType.number,
              style: TextStyle(color: AppColorsTheme.textPrimary(context)),
              onChanged: (_) => _updateData(),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(3),
              ],
              decoration: InputDecoration(
                hintText: 'Enter your age',
                hintStyle: TextStyle(color: AppColorsTheme.textSecondary(context)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your age';
                }
                final age = int.tryParse(value);
                if (age == null || age < 13 || age > 100) {
                  return 'Please enter a valid age';
                }
                return null;
              },
            ),
          ),

          const SizedBox(height: 24),
          
          // Height Field
          Text(
            'Height*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColorsTheme.borderLight(context)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _heightController,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                    onChanged: (_) => _updateData(),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    decoration: InputDecoration(
                      hintText: 'Enter your height',
                      hintStyle: TextStyle(color: AppColorsTheme.textSecondary(context)),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your height';
                      }
                      final height = double.tryParse(value);
                      if (height == null || height <= 0) {
                        return 'Please enter a valid height';
                      }
                      return null;
                    },
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: DropdownButton<String>(
                    value: widget.onboardingData.heightUnit,
                    dropdownColor: AppColorsTheme.cardBackground(context),
                    underline: Container(),
                    style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                    items: [
                      DropdownMenuItem(
                        value: 'cm',
                        child: Text('cm', style: TextStyle(color: AppColorsTheme.textPrimary(context))),
                      ),
                      DropdownMenuItem(
                        value: 'ft',
                        child: Text('ft', style: TextStyle(color: AppColorsTheme.textPrimary(context))),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        widget.onboardingData.heightUnit = value!;
                      });
                      _updateData();
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Weight Field
          Text(
            'Weight*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColorsTheme.borderLight(context)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                    onChanged: (_) => _updateData(),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    decoration: InputDecoration(
                      hintText: 'Enter your weight',
                      hintStyle: TextStyle(color: AppColorsTheme.textSecondary(context)),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your weight';
                      }
                      final weight = double.tryParse(value);
                      if (weight == null || weight <= 0) {
                        return 'Please enter a valid weight';
                      }
                      return null;
                    },
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: DropdownButton<String>(
                    value: widget.onboardingData.weightUnit,
                    dropdownColor: AppColorsTheme.cardBackground(context),
                    underline: Container(),
                    style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                    items: [
                      DropdownMenuItem(
                        value: 'kg',
                        child: Text('kg', style: TextStyle(color: AppColorsTheme.textPrimary(context))),
                      ),
                      DropdownMenuItem(
                        value: 'lbs',
                        child: Text('lbs', style: TextStyle(color: AppColorsTheme.textPrimary(context))),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        widget.onboardingData.weightUnit = value!;
                      });
                      _updateData();
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}