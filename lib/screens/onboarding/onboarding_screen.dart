import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/onboarding_data.dart';
import '../../services/profile_service.dart';
import '../../design_system/design_system.dart';
import 'steps/personal_info_step.dart';
import 'steps/fitness_goals_step.dart';
import 'steps/fitness_experience_step.dart';
import 'steps/workout_preferences_step.dart';
import 'steps/equipment_step.dart';
import 'steps/health_dietary_step.dart';
import 'steps/review_submit_step.dart';

// Reusable onboarding template widget
class OnboardingTemplate extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String stepTitle;
  final String stepSubtitle;
  final Widget child;
  final VoidCallback? onBack;
  final VoidCallback? onContinue;
  final bool canContinue;
  final bool isTestMode;
  final bool hideFooter;

  const OnboardingTemplate({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitle,
    required this.stepSubtitle,
    required this.child,
    this.onBack,
    this.onContinue,
    this.canContinue = true,
    this.isTestMode = false,
    this.hideFooter = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Column(
        children: [
            // Header Section
            Container(
              color: AppColorsTheme.surface(context),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Brand Header
                    Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.fitness_center,
                            color: AppColorsTheme.surface(context),
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'OpenFit',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const Text(
                              'by OpenThrive',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        if (isTestMode)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const Text(
                              'TEST MODE',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    
                    // Step Progress
                    Text(
                      'Step $currentStep of $totalSteps',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    // Progress Bar
                    LinearProgressIndicator(
                      value: currentStep / totalSteps,
                      backgroundColor: Colors.grey[200],
                      valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                      minHeight: 6,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ],
                ),
              ),
            ),
            
            // Content Area
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  color: AppColorsTheme.surface(context),
                  width: double.infinity,
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          stepTitle,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          stepSubtitle,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 32),
                        child,
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Footer Section (conditionally rendered)
            if (!hideFooter)
              Container(
                color: AppColorsTheme.surface(context),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // Navigation Buttons
                      Row(
                        children: [
                          if (onBack != null)
                            Expanded(
                              child: AppButton(
                                text: 'Back',
                                icon: Icons.arrow_back,
                                variant: AppButtonVariant.outline,
                                fullWidth: true,
                                onPressed: onBack,
                              ),
                            ),
                        if (onBack != null) const SizedBox(width: 16),
                        Expanded(
                          flex: onBack != null ? 1 : 2,
                          child: AppButton(
                            text: stepTitle == 'Your Fitness Plan is Ready' ? 'Finish' : 'Continue',
                            icon: stepTitle == 'Your Fitness Plan is Ready' ? Icons.check : Icons.arrow_forward,
                            onPressed: canContinue ? onContinue : null,
                            fullWidth: true,
                            size: AppButtonSize.large,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Bottom Links
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            // Handle Learn More
                          },
                          child: const Text(
                            'Learn More',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Text('•', style: TextStyle(color: Colors.grey)),
                        TextButton(
                          onPressed: () {
                            // Handle How It Works
                          },
                          child: const Text(
                            'How It Works',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Text('•', style: TextStyle(color: Colors.grey)),
                        TextButton(
                          onPressed: () {
                            // Handle Pricing
                          },
                          child: const Text(
                            'Pricing',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
        ],
      ),
    );
  }
}

class OnboardingScreen extends StatefulWidget {
  final bool isTestMode;
  
  const OnboardingScreen({
    super.key,
    this.isTestMode = false,
  });

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  late OnboardingData _onboardingData;
  int _currentStep = 0;
  final int _totalSteps = 7;
  bool _isLoading = true;

  final List<Map<String, String>> _stepInfo = [
    {
      'title': 'Tell Us About Yourself',
      'subtitle': 'Help us personalize your fitness journey',
    },
    {
      'title': 'What Are Your Fitness Goals?',
      'subtitle': 'Select all that apply',
    },
    {
      'title': 'Current Fitness Levels',
      'subtitle': 'Help us understand where you are in your fitness journey',
    },
    {
      'title': 'Your Workout Schedule',
      'subtitle': 'When science meets your lifestyle',
    },
    {
      'title': 'Select Equipment',
      'subtitle': 'Select all equipment you have access to',
    },
    {
      'title': 'Anything else we should know?',
      'subtitle': 'Share any additional information that might help us personalize your experience better',
    },
    {
      'title': 'Your Fitness Plan is Ready',
      'subtitle': 'Based on your inputs',
    },
  ];

  @override
  void initState() {
    super.initState();
    // Use post-frame callback to ensure context is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingProfile();
    });
  }

  Future<void> _loadExistingProfile() async {
    try {
      final profileService = context.read<ProfileService>();
      final existingProfile = await profileService.getUserProfile();
      
      if (existingProfile != null) {
        debugPrint('Existing profile found with ${existingProfile.keys.length} fields');
        debugPrint('Sample data: name=${existingProfile['name']}, age=${existingProfile['age']}, gender=${existingProfile['gender']}');
        // Convert existing profile to OnboardingData
        _onboardingData = profileService.profileToOnboardingData(existingProfile) ?? OnboardingData();
        debugPrint('Converted onboarding data:');
        debugPrint('  Name: ${_onboardingData.name}');
        debugPrint('  Age: ${_onboardingData.age}');
        debugPrint('  Gender: ${_onboardingData.gender}');
        debugPrint('  Height: ${_onboardingData.height}');
        debugPrint('  Primary Goal: ${_onboardingData.primaryGoal}');
      } else {
        debugPrint('No existing profile found, creating new OnboardingData');
        _onboardingData = OnboardingData();
      }
    } catch (e) {
      debugPrint('Error loading existing profile: $e');
      _onboardingData = OnboardingData();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _submitOnboarding() async {
    if (widget.isTestMode) {
      // Test mode - just show success and go back
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test completed! (No data saved)'),
            backgroundColor: Colors.blue,
          ),
        );
        Navigator.of(context).pop();
      }
      return;
    }
    
    try {
      final profileService = context.read<ProfileService>();
      await profileService.saveOnboardingData(_onboardingData);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile setup completed!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onDataChanged() {
    setState(() {
      // Trigger rebuild to update continue button state
    });
  }

  Widget _buildStepContent(int index) {
    switch (index) {
      case 0:
        return PersonalInfoStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 1:
        return FitnessGoalsStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 2:
        return FitnessExperienceStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 3:
        return WorkoutPreferencesStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 4:
        return EquipmentStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 5:
        return HealthDietaryStep(
          onboardingData: _onboardingData,
          onDataChanged: _onDataChanged,
        );
      case 6:
        return ReviewSubmitStep(
          onboardingData: _onboardingData,
        );
      default:
        return const SizedBox();
    }
  }

  bool _canContinue() {
    switch (_currentStep) {
      case 0:
        return _onboardingData.name?.isNotEmpty == true &&
               _onboardingData.gender?.isNotEmpty == true &&
               _onboardingData.age != null &&
               _onboardingData.height != null &&
               _onboardingData.weight != null;
      case 1:
        return _onboardingData.primaryGoal?.isNotEmpty == true;
      case 2:
        return _onboardingData.cardioLevel != null &&
               _onboardingData.weightliftingLevel != null;
      case 3:
        return _onboardingData.workoutFrequency != null &&
               _onboardingData.workoutDuration?.isNotEmpty == true;
      case 4:
        return _onboardingData.equipment.isNotEmpty;
      case 5:
        return true; // Health & dietary info is optional
      case 6:
        return true; // Review step
      default:
        return false;
    }
  }

  void _handleContinue() {
    if (_currentStep == _totalSteps - 1) {
      _submitOnboarding();
    } else {
      _nextStep();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFFF8FAFC),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              SizedBox(height: 16),
              Text(
                'Loading your profile...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final stepInfo = _stepInfo[_currentStep];
    
    return OnboardingTemplate(
      currentStep: _currentStep + 1,
      totalSteps: _totalSteps,
      stepTitle: stepInfo['title']!,
      stepSubtitle: stepInfo['subtitle']!,
      isTestMode: widget.isTestMode,
      onBack: _currentStep > 0 ? _previousStep : null,
      onContinue: _handleContinue,
      canContinue: _canContinue(),
      hideFooter: _currentStep == _totalSteps - 1, // Hide footer on final step
      child: _buildStepContent(_currentStep),
    );
  }
}