import 'package:flutter/material.dart';
import '../../design_system/design_system.dart';
import '../../design_system/theme_extensions.dart';

/// Simple dev-only style guide to visualise tokens & components.
class StyleGuideScreen extends StatelessWidget {
  const StyleGuideScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final spacing = Theme.of(context).extension<SpacingTokens>()!;

    return AppScaffold(
      title: 'Style Guide',
      canPop: true,
      body: Padding(
        padding: EdgeInsets.all(spacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildColorsSection(context),
            const SizedBox(height: AppSpacing.sectionSpacing),
            _buildTypographySection(context),
            const SizedBox(height: AppSpacing.sectionSpacing),
            _buildComponentsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildColorsSection(BuildContext context) {
    final colors = {
      'Primary': AppColors.primary,
      'Primary Light': AppColors.primaryLight,
      'Success': AppColors.success,
      'Warning': AppColors.warning,
      'Error': AppColors.error,
      'Background': AppColorsTheme.background(context),
      'Surface': AppColorsTheme.surface(context),
      'Card': AppColorsTheme.cardBackground(context),
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Colors', style: AppTypography.heading1),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.md,
          runSpacing: AppSpacing.md,
          children: colors.entries
              .map((e) => _ColorSwatch(label: e.key, color: e.value))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildTypographySection(BuildContext context) {
    final samples = {
      'Display Large': Theme.of(context).textTheme.displayLarge,
      'Headline Medium': Theme.of(context).textTheme.headlineMedium,
      'Body Large': Theme.of(context).textTheme.bodyLarge,
      'Caption': Theme.of(context).textTheme.bodySmall,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Typography', style: AppTypography.heading1),
        const SizedBox(height: AppSpacing.md),
        ...samples.entries.map((e) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Text(e.key, style: e.value),
            )),
      ],
    );
  }

  Widget _buildComponentsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Components', style: AppTypography.heading1),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.md,
          runSpacing: AppSpacing.md,
          children: const [
            AppButton(text: 'Primary'),
            AppButton(text: 'Secondary', variant: AppButtonVariant.secondary),
            AppButton(text: 'Outline', variant: AppButtonVariant.outline),
            AppCard(child: Text('Example Card')),
          ],
        ),
      ],
    );
  }
}

class _ColorSwatch extends StatelessWidget {
  final String label;
  final Color color;

  const _ColorSwatch({required this.label, required this.color});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: AppTypography.caption,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
