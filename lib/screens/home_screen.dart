import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/profile_service.dart';
import '../design_system/design_system.dart';


import 'onboarding/onboarding_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Future<Map<String, dynamic>?> _profileFuture;

  @override
  void initState() {
    super.initState();
    _profileFuture = context.read<ProfileService>().getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    final authService = context.watch<AuthService>();
    
    return AppScaffold(
      
      body: Padding(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: FutureBuilder<Map<String, dynamic>?>(
          future: _profileFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              );
            }
            
            final profile = snapshot.data;
            final userName = profile?['display_name'] ?? 
                            profile?['name'] ?? 
                            (authService.currentUser?.email?.split('@').first) ?? 
                            'User';
            final hasCompletedOnboarding = profile?['onboarding_completed'] ?? false;
            
            if (!hasCompletedOnboarding) {
              return _buildOnboardingPrompt(context, userName);
            }
            
            return _buildMainContent(context, userName, profile);
          },
        ),
      ),
    );
  }

  Widget _buildOnboardingPrompt(BuildContext context, String userName) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, userName),
          const SizedBox(height: AppSpacing.sectionSpacing),
          AppCard(
            padding: const EdgeInsets.all(AppSpacing.sectionSpacing),
            borderRadius: AppBorderRadius.modalRadius,
            boxShadow: AppShadows.elevation3,
            child: Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: AppBorderRadius.cardRadius,
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    size: 40,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: AppSpacing.xxl),
                Text(
                  'Complete Your Profile',
                  style: AppTypography.heading1,
                ),
                const SizedBox(height: AppSpacing.md),
                Text(
                  'Set up your fitness preferences to get personalized workouts tailored just for you.',
                  textAlign: TextAlign.center,
                  style: AppTypography.body1.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                const SizedBox(height: AppSpacing.sectionSpacing),
                AppButton(
                  text: 'Get Started',
                  onPressed: () {
                    Navigator.pushNamed(context, '/onboarding');
                  },
                  size: AppButtonSize.large,
                  fullWidth: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, String userName, Map<String, dynamic>? profile) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, userName),
          Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildReadyToTrain(context, profile),
                const SizedBox(height: AppSpacing.sectionSpacing),
                _buildThisWeekStats(context),
                const SizedBox(height: AppSpacing.xxl),
                _buildQuickActions(context),
                const SizedBox(height: AppSpacing.sectionSpacing),
                // Debug section - can be removed in production
                Center(
                  child: TextButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const OnboardingScreen(isTestMode: true),
                        ),
                      );
                    },
                    icon: const Icon(Icons.bug_report, size: 16),
                    label: const Text('Test Onboarding'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[400],
                    ),
                  ),
                ),
              ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, String userName) {
    final authService = context.read<AuthService>();
    
    return Container(
      color: AppColorsTheme.surface(context),
      padding: const EdgeInsets.all(AppSpacing.screenPadding),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hello, ${userName.split(' ').first}!',
                style: AppTypography.display2.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              Text(
                'Ready to crush your goals?',
                style: AppTypography.body1.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
          const Spacer(),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: AppBorderRadius.buttonRadius,
            ),
            child: IconButton(
              icon: const Icon(
                Icons.logout,
                color: AppColors.primary,
                size: 20,
              ),
              onPressed: () async {
                final confirm = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: AppBorderRadius.cardRadius,
                    ),
                    title: const Text('Sign Out'),
                    content: const Text('Are you sure you want to sign out?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context, false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context, true),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                        child: const Text('Sign Out'),
                      ),
                    ],
                  ),
                );
                
                if (confirm == true) {
                  await authService.signOut();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadyToTrain(BuildContext context, Map<String, dynamic>? profile) {
    // Generate workout based on user's profile
    final workoutData = _generateWorkoutRecommendation(profile);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ready to train?',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your next workout is optimized and ready',
          style: TextStyle(
            fontSize: 16,
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        const SizedBox(height: 24),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColorsTheme.cardBackground(context),
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppShadows.getElevation(context, 3),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: const Color(0xFF6366F1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      workoutData['icon'] as IconData,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          workoutData['title'] as String,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppColorsTheme.textPrimary(context),
                          ),
                        ),
                        Text(
                          workoutData['subtitle'] as String,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColorsTheme.textSecondary(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  _buildWorkoutStat(
                    Icons.access_time,
                    'Duration',
                    workoutData['duration'] as String,
                  ),
                  const SizedBox(width: 24),
                  _buildWorkoutStat(
                    Icons.local_fire_department,
                    'Calories',
                    workoutData['calories'] as String,
                  ),
                  const SizedBox(width: 24),
                  _buildWorkoutStat(
                    Icons.refresh,
                    'Sets',
                    workoutData['sets'] as String,
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Text(
                'Today\'s Focus',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: (workoutData['exercises'] as List<String>).map((exercise) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primary.withValues(alpha: 0.15)
                          : const Color(0xFFF1F5F9),
                      borderRadius: BorderRadius.circular(20),
                      border: Theme.of(context).brightness == Brightness.dark
                          ? Border.all(
                              color: AppColors.primary.withValues(alpha: 0.3),
                              width: 1,
                            )
                          : null,
                    ),
                    child: Text(
                      exercise,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primary
                            : AppColorsTheme.textPrimary(context),
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Navigate to workout
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6366F1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.play_arrow, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'Start Workout',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    // TODO: Navigate to workout details
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Color(0xFF6366F1)),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.list, size: 20, color: Color(0xFF6366F1)),
                      SizedBox(width: 8),
                      Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF6366F1),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutStat(IconData icon, String label, String value) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            size: 20,
            color: const Color(0xFF6366F1),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThisWeekStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Week',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Row(
          children: [
            Expanded(
              child: AppStatCard(
                icon: Icons.trending_up,
                iconColor: AppColors.success,
                value: '4',
                label: 'Workouts',
                subtitle: '+1 from last week',
              ),
            ),
            const SizedBox(width: AppSpacing.lg),
            Expanded(
              child: AppStatCard(
                icon: Icons.local_fire_department,
                iconColor: AppColors.warning,
                value: '12',
                label: 'Streak',
                subtitle: 'days active',
              ),
            ),
          ],
        ),
      ],
    );
  }



  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: AppSpacing.lg,
          crossAxisSpacing: AppSpacing.lg,
          childAspectRatio: 1.2,
          children: [
            _buildActionCard(
              'Browse Exercises',
              Icons.fitness_center,
              const Color(0xFF10B981),
              () {
                // TODO: Navigate to exercises
              },
            ),
            _buildActionCard(
              'My Progress',
              Icons.trending_up,
              const Color(0xFF8B5CF6),
              () {
                // TODO: Navigate to progress
              },
            ),
            _buildActionCard(
              'Nutrition',
              Icons.restaurant,
              const Color(0xFFF59E0B),
              () {
                // TODO: Navigate to nutrition
              },
            ),
            _buildActionCard(
              'Profile',
              Icons.person,
              const Color(0xFF6366F1),
              () {
                Navigator.pushNamed(context, '/onboarding');
              },
            ),
          ],
        ),
      ],
    );
  }

  Map<String, dynamic> _generateWorkoutRecommendation(Map<String, dynamic>? profile) {
    // Generate workout based on user profile
    final primaryGoal = profile?['fitness_goal_primary'] ?? 
                      profile?['fitness_goal'] ?? 
                      profile?['primarygoal'];

    
    // Determine workout type based on goals
    if (primaryGoal?.toLowerCase().contains('strength') == true || 
        primaryGoal?.toLowerCase().contains('muscle') == true) {
      return {
        'title': 'Upper Body Strength',
        'subtitle': 'Build muscle and strength',
        'duration': '45 min',
        'calories': '320 kcal',
        'sets': '4 x 8',
        'icon': Icons.fitness_center,
        'exercises': ['Push-ups', 'Pull-ups', 'Dumbbell rows'],
      };
    } else if (primaryGoal?.toLowerCase().contains('cardio') == true ||
               primaryGoal?.toLowerCase().contains('stamina') == true) {
      return {
        'title': 'Cardio Blast',
        'subtitle': 'Improve cardiovascular health',
        'duration': '30 min',
        'calories': '280 kcal',
        'sets': '5 rounds',
        'icon': Icons.directions_run,
        'exercises': ['Burpees', 'Mountain climbers', 'Jump rope'],
      };
    } else if (primaryGoal?.toLowerCase().contains('weight loss') == true) {
      return {
        'title': 'Fat Burn Circuit',
        'subtitle': 'High-intensity fat burning',
        'duration': '35 min',
        'calories': '350 kcal',
        'sets': '3 x 12',
        'icon': Icons.local_fire_department,
        'exercises': ['Squats', 'Burpees', 'Planks'],
      };
    } else {
      return {
        'title': 'Full Body Workout',
        'subtitle': 'Complete fitness routine',
        'duration': '40 min',
        'calories': '300 kcal',
        'sets': '3 x 10',
        'icon': Icons.fitness_center,
        'exercises': ['Squats', 'Push-ups', 'Planks'],
      };
    }
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.getElevation(context, 2),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: color,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColorsTheme.textPrimary(context),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class HomeScreenContent extends StatelessWidget {
  const HomeScreenContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const HomeScreen();
  }
}