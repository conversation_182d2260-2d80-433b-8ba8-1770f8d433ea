import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/profile_service.dart';
import '../design_system/design_system.dart';
import '../components/theme_toggle.dart';
import 'onboarding/onboarding_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late Future<Map<String, dynamic>?> _profileFuture;

  @override
  void initState() {
    super.initState();
    _profileFuture = context.read<ProfileService>().getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    final authService = context.watch<AuthService>();

    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: SafeArea(
        child: FutureBuilder<Map<String, dynamic>?>(
          future: _profileFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              );
            }

            final profile = snapshot.data;
            final userName = profile?['name'] ?? 
                            profile?['display_name'] ?? 
                            (authService.currentUser?.email?.split('@').first) ?? 
                            'User';

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Profile',
                      style: AppTypography.display2.copyWith(
                        color: AppColorsTheme.textPrimary(context),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Manage your account and preferences',
                      style: AppTypography.body1.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // Profile Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColorsTheme.cardBackground(context),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: AppShadows.getElevation(context, 3),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.person,
                              size: 40,
                              color: Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            userName,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppColorsTheme.textPrimary(context),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            authService.currentUser?.email ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColorsTheme.textSecondary(context),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Settings Options
                    _buildSettingsSection(context, profile),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context, Map<String, dynamic>? profile) {
    return Column(
      children: [
        // Theme Toggle
        const ThemeToggle(),
        const SizedBox(height: 24),

        _buildSettingsItem(
          icon: Icons.edit,
          title: 'Edit Profile',
          subtitle: 'Update your fitness preferences',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OnboardingScreen(),
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.notifications,
          title: 'Notifications',
          subtitle: 'Manage workout reminders',
          onTap: () {
            // TODO: Navigate to notifications settings
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.privacy_tip,
          title: 'Privacy',
          subtitle: 'Data and privacy settings',
          onTap: () {
            // TODO: Navigate to privacy settings
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.help,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onTap: () {
            // TODO: Navigate to help
          },
        ),
        const SizedBox(height: 24),
        _buildSettingsItem(
          icon: Icons.logout,
          title: 'Sign Out',
          subtitle: 'Sign out of your account',
          isDestructive: true,
          onTap: () async {
            final authService = context.read<AuthService>();
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                backgroundColor: AppColorsTheme.surface(context),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'Sign Out',
                  style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                ),
                content: Text(
                  'Are you sure you want to sign out?',
                  style: TextStyle(color: AppColorsTheme.textSecondary(context)),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                    child: const Text('Sign Out'),
                  ),
                ],
              ),
            );
            
            if (confirm == true) {
              await authService.signOut();
            }
          },
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Builder(
      builder: (context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.getCardShadow(context),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isDestructive 
                        ? Colors.red.withValues(alpha: 0.1)
                        : const Color(0xFF6366F1).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: isDestructive ? Colors.red : const Color(0xFF6366F1),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDestructive ? Colors.red : AppColorsTheme.textPrimary(context),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColorsTheme.textSecondary(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
      },
    );
  }
}