import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import 'home_screen.dart';
import 'workouts_screen.dart';
import 'progress_screen.dart';
import 'profile_screen.dart';

class MainAppScreen extends StatefulWidget {
  const MainAppScreen({super.key});

  @override
  State<MainAppScreen> createState() => _MainAppScreenState();
}

class _MainAppScreenState extends State<MainAppScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreenContent(),
    const WorkoutsScreen(),
    const ProgressScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentItem: _getCurrentNavigationItem(),
        onItemTapped: _onNavigationItemTapped,
      ),
    );
  }

  AppNavigationItem _getCurrentNavigationItem() {
    switch (_currentIndex) {
      case 0:
        return AppNavigationItem.home;
      case 1:
        return AppNavigationItem.workouts;
      case 2:
        return AppNavigationItem.progress;
      case 3:
        return AppNavigationItem.profile;
      default:
        return AppNavigationItem.home;
    }
  }

  void _onNavigationItemTapped(AppNavigationItem item) {
    int newIndex;
    switch (item) {
      case AppNavigationItem.home:
        newIndex = 0;
        break;
      case AppNavigationItem.workouts:
        newIndex = 1;
        break;
      case AppNavigationItem.progress:
        newIndex = 2;
        break;
      case AppNavigationItem.profile:
        newIndex = 3;
        break;
    }
    
    setState(() {
      _currentIndex = newIndex;
    });
  }


}