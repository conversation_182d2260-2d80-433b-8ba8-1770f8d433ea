class Exercise {
  final String id;
  final String name;
  final String? description;
  final String? videoUrl;
  final String? primaryMuscle;
  final String? secondaryMuscle;
  final String? equipment;
  final String? category;
  final String? instructions;
  final String? verticalVideo;

  Exercise({
    required this.id,
    required this.name,
    this.description,
    this.videoUrl,
    this.primaryMuscle,
    this.secondaryMuscle,
    this.equipment,
    this.category,
    this.instructions,
    this.verticalVideo,
  });

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      videoUrl: json['video_url'],
      primaryMuscle: json['primary_muscle'],
      secondaryMuscle: json['secondary_muscle'],
      equipment: json['equipment'],
      category: json['category'],
      instructions: json['instructions'],
      verticalVideo: json['vertical_video'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'video_url': videoUrl,
      'primary_muscle': primaryMuscle,
      'secondary_muscle': secondaryMuscle,
      'equipment': equipment,
      'category': category,
      'instructions': instructions,
      'vertical_video': verticalVideo,
    };
  }
}

class WorkoutExercise {
  final String id;
  final String workoutId;
  final String exerciseId;
  final Exercise exercise;
  final int sets;
  final List<int> reps;
  final List<int>? weight;
  final int orderIndex;
  final int? restInterval;
  final String name;
  final bool completed;

  WorkoutExercise({
    required this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.exercise,
    required this.sets,
    required this.reps,
    this.weight,
    required this.orderIndex,
    this.restInterval,
    required this.name,
    this.completed = false,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      id: json['id'],
      workoutId: json['workout_id'],
      exerciseId: json['exercise_id'],
      exercise: Exercise.fromJson(json['exercise'] ?? {}),
      sets: json['sets'] ?? 0,
      reps: json['reps'] != null ? List<int>.from(json['reps']) : [],
      weight: json['weight'] != null ? List<int>.from(json['weight']) : null,
      orderIndex: json['order_index'] ?? 0,
      restInterval: json['rest_interval'],
      name: json['name'] ?? '',
      completed: json['completed'] ?? false,
    );
  }

  String get formattedReps {
    if (reps.isEmpty) return '';
    if (reps.every((rep) => rep == reps.first)) {
      return '$sets×${reps.first}';
    }
    return reps.join(', ');
  }

  String get formattedRestTime {
    if (restInterval == null) return '';
    if (restInterval! < 60) return '${restInterval}s';
    final minutes = restInterval! ~/ 60;
    final seconds = restInterval! % 60;
    return seconds > 0 ? '${minutes}m ${seconds}s' : '${minutes}m';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'exercise': exercise.toJson(),
      'sets': sets,
      'reps': reps,
      'weight': weight,
      'order_index': orderIndex,
      'rest_interval': restInterval,
      'name': name,
      'completed': completed,
    };
  }
}