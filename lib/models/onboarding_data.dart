class OnboardingData {
  // Personal Info
  String? name;
  String? gender;
  int? age;
  double? height;
  String heightUnit;
  double? weight;
  String weightUnit;

  // Fitness Goals
  String? primaryGoal;
  List<String> fitnessGoals;

  // Experience Level
  String? fitnessExperience;
  int? cardioLevel;
  int? weightliftingLevel;

  // Workout Preferences
  int? workoutFrequency;
  String? workoutDuration;
  List<String> workoutDays;
  List<String> workoutEnvironment;

  // Equipment
  List<String> equipment;

  // Health & Dietary
  List<String> healthConditions;
  List<String> physicalLimitations;
  List<String> dietaryRestrictions;
  String? additionalNotes;

  OnboardingData({
    this.name,
    this.gender,
    this.age,
    this.height,
    this.heightUnit = 'cm',
    this.weight,
    this.weightUnit = 'kg',
    this.primaryGoal,
    List<String>? fitnessGoals,
    this.fitnessExperience,
    this.cardioLevel,
    this.weightliftingLevel,
    this.workoutFrequency,
    this.workoutDuration,
    List<String>? workoutDays,
    List<String>? workoutEnvironment,
    List<String>? equipment,
    List<String>? healthConditions,
    List<String>? physicalLimitations,
    List<String>? dietaryRestrictions,
    this.additionalNotes,
  })  : fitnessGoals = fitnessGoals ?? [],
        workoutDays = workoutDays ?? [],
        workoutEnvironment = workoutEnvironment ?? [],
        equipment = equipment ?? [],
        healthConditions = healthConditions ?? [],
        physicalLimitations = physicalLimitations ?? [],
        dietaryRestrictions = dietaryRestrictions ?? [];

  Map<String, dynamic> toJson() {
    return {
      'display_name': name,
      'gender': gender,
      'age': age,
      'height': height,
      'height_unit': heightUnit,
      'weight': weight,
      'weight_unit': weightUnit,
      'fitness_goal_primary': primaryGoal,
      'fitness_goals_array': fitnessGoals,
      'fitness_experience': fitnessExperience,
      'cardio_fitness_level': cardioLevel,
      'weightlifting_fitness_level': weightliftingLevel,
      'workout_frequency': workoutFrequency,
      'workout_duration': workoutDuration,
      'workout_days': workoutDays,
      'workout_environment': workoutEnvironment,
      'equipment': equipment,
      'health_conditions': healthConditions,
      'physical_limitations': physicalLimitations,
      'dietary_restrictions': dietaryRestrictions,
      'additional_notes': additionalNotes,
      'onboarding_completed': true,
      'has_completed_preferences': true,
      'updated_at': DateTime.now().toIso8601String(),
    };
  }
}

class OnboardingConstants {
  static const List<String> genders = ['Male', 'Female', 'Other', 'Prefer not to say'];
  
  static const List<String> fitnessGoals = [
    'Training for a specific sport',
    'Increase strength',
    'Increase stamina',
    'Optimize Health and Fitness',
    'Build muscle mass and size',
    'Weight loss',
    'Improve Flexibility',
    'General Fitness',
    'Stress Relief',
  ];
  
  static const List<String> experienceLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];
  
  static const List<String> workoutDurations = [
    '15-30 minutes',
    '30-45 minutes',
    '45-60 minutes',
    '60+ minutes',
  ];
  
  static const List<String> weekDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];
  
  static const List<String> workoutEnvironments = [
    'Home',
    'Gym',
    'Outdoor',
    'Office',
  ];
  
  static const List<String> equipmentOptions = [
    'No Equipment',
    'Dumbbells',
    'Barbell',
    'Resistance Bands',
    'Pull-up Bar',
    'Kettlebells',
    'Medicine Ball',
    'Jump Rope',
    'Yoga Mat',
    'Foam Roller',
    'Bench',
    'Cable Machine',
    'Cardio Machines',
  ];
  
  static const List<String> healthConditions = [
    'None',
    'High Blood Pressure',
    'Diabetes',
    'Heart Condition',
    'Asthma',
    'Joint Problems',
    'Back Pain',
    'Pregnancy',
    'Other',
  ];
  
  static const List<String> dietaryOptions = [
    'None',
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Keto',
    'Paleo',
    'Low-Carb',
    'Intermittent Fasting',
  ];
}