# iOS TestFlight Deployment Workflow

## Overview
This document outlines the complete workflow for deploying OpenFit v2 to Apple TestFlight for beta testing.

## Prerequisites

### Apple Developer Account
- ✅ Active Apple Developer Program membership
- ✅ Team ID: `HP284BJ924`
- ✅ Bundle ID registered: `com.abenezernuro.agenticfit`

### Development Environment
- ✅ Xcode 15+ installed
- ✅ Flutter 3.32+ with Dart 3.8+
- ✅ CocoaPods installed (`/opt/homebrew/bin/pod`)
- ✅ Apple Transporter app (for uploads)

### App Store Connect Setup
- ✅ App created: "OpenFit"
- ✅ Bundle ID: `com.abenezernuro.agenticfit`
- ✅ SKU: `OPENFIT12`
- ✅ Team access configured

## Step-by-Step Deployment Process

### 1. Pre-Build Preparation

```bash
# Navigate to project directory
cd /path/to/openfitv2

# Clean previous builds
flutter clean

# Get dependencies
flutter pub get

# Install iOS dependencies
cd ios && /opt/homebrew/bin/pod install && cd ..
```

### 2. Verify Configuration

Check these critical settings:

**Bundle Identifier** (in `ios/Runner.xcodeproj/project.pbxproj`):
```
PRODUCT_BUNDLE_IDENTIFIER = com.abenezernuro.agenticfit;
```

**Team ID** (should be automatically set):
```
DEVELOPMENT_TEAM = HP284BJ924;
```

**Version & Build Number** (in `pubspec.yaml`):
```yaml
version: 1.0.0+1
```

### 3. Build for Release

```bash
# Add CocoaPods to PATH and build IPA
export PATH="/opt/homebrew/bin:$PATH"
flutter build ipa --release
```

**Expected Output:**
```
✓ Built build/ios/archive/Runner.xcarchive (214.4MB)
✓ Built IPA to build/ios/ipa (39.9MB)
Bundle Identifier: com.abenezernuro.agenticfit
```

### 4. Verify Build

Check the distribution summary:
```bash
# Verify bundle identifier in the IPA
grep -A 1 "application-identifier" build/ios/ipa/DistributionSummary.plist
```

Should show: `HP284BJ924.com.abenezernuro.agenticfit`

### 5. Upload to App Store Connect

#### Option A: Apple Transporter (Recommended)
1. **Download Apple Transporter** from Mac App Store
2. **Open Transporter**
3. **Sign in** with your Apple ID (must have access to the app)
4. **Drag and drop** `build/ios/ipa/openfitv2.ipa`
5. **Click "Deliver"**

#### Option B: Xcode Organizer
1. **Open Xcode**
2. **Window → Organizer → Archives**
3. **Select latest Runner archive**
4. **Distribute App → App Store Connect**
5. **Follow upload wizard**

#### Option C: Command Line
```bash
xcrun altool --upload-app --type ios \
  -f build/ios/ipa/openfitv2.ipa \
  --username YOUR_APPLE_ID \
  --password YOUR_APP_SPECIFIC_PASSWORD
```

### 6. Post-Upload Process

1. **Wait 10-30 minutes** for Apple to process the build
2. **Check App Store Connect** → OpenFit → TestFlight
3. **Build will appear** once processing is complete
4. **Add internal testers** and send invites

## Common Issues & Solutions

### Bundle Identifier Mismatch
**Error:** "No suitable application records were found"

**Solution:**
1. Verify bundle ID in App Store Connect matches exactly
2. Check `ios/Runner.xcodeproj/project.pbxproj` has correct bundle ID
3. Rebuild IPA after fixing bundle ID

### CocoaPods Not Found
**Error:** "CocoaPods not installed or not in valid state"

**Solution:**
```bash
# Add CocoaPods to PATH
export PATH="/opt/homebrew/bin:$PATH"

# Or install if missing
sudo gem install cocoapods
```

### Code Signing Issues
**Error:** Code signing failures

**Solution:**
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select Runner target → Signing & Capabilities
3. Verify "Automatically manage signing" is enabled
4. Confirm correct team is selected

### Archive Not Found
**Error:** No archives in Xcode Organizer

**Solution:**
1. Build using `flutter build ios --release` first
2. Then run `flutter build ipa --release`
3. Check Xcode Organizer for archives

## Version Management

### Incrementing Version
```yaml
# In pubspec.yaml
version: 1.0.1+2  # version+build_number
```

### Build Number Rules
- Must be unique for each upload
- Increment for each TestFlight build
- Can be same across different versions

## TestFlight Configuration

### Internal Testing
- Up to 100 internal testers
- No App Review required
- Immediate access after upload

### External Testing
- Up to 10,000 external testers
- Requires App Review (1-7 days)
- Need to provide test information

## Automation Opportunities

### CI/CD Pipeline
Consider automating with GitHub Actions:
```yaml
# .github/workflows/ios-deploy.yml
name: iOS Deploy
on:
  push:
    tags: ['v*']
jobs:
  deploy:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter build ipa --release
      # Add upload steps
```

## Security Notes

- Never commit certificates or provisioning profiles
- Use App Store Connect API keys for automation
- Rotate API keys regularly
- Use environment variables for sensitive data

## Resources

- [Apple Developer Documentation](https://developer.apple.com/documentation/)
- [TestFlight Beta Testing](https://developer.apple.com/testflight/)
- [Flutter iOS Deployment](https://docs.flutter.dev/deployment/ios)
- [App Store Connect Help](https://help.apple.com/app-store-connect/)

## Quick Reference Commands

```bash
# Complete build and deploy workflow
flutter clean
flutter pub get
cd ios && /opt/homebrew/bin/pod install && cd ..
export PATH="/opt/homebrew/bin:$PATH"
flutter build ipa --release

# Upload via command line
xcrun altool --upload-app --type ios \
  -f build/ios/ipa/openfitv2.ipa \
  --username YOUR_APPLE_ID \
  --password YOUR_APP_SPECIFIC_PASSWORD
```
