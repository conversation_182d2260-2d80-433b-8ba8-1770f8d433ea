# OpenFit v2

A comprehensive Flutter-based fitness application with Supabase backend integration, designed for cross-platform deployment with iOS TestFlight support.

## 🚀 Quick Start

### Prerequisites
- Flutter 3.32+ with Dart 3.8+
- Xcode 15+ (for iOS development)
- CocoaPods installed
- Apple Developer Account (for iOS deployment)

### Installation
```bash
# Clone and setup
git clone <repository-url>
cd openfitv2
flutter pub get

# iOS setup
cd ios && pod install && cd ..
```

### Development
```bash
# Run the app
flutter run

# Run tests
flutter test

# Build for release
flutter build ios --release
flutter build ipa --release
```

## 📱 Platform Support

- ✅ iOS (Primary target)
- ✅ Android
- ✅ Web
- ✅ macOS
- ✅ Linux
- ✅ Windows

## 🏗️ Architecture

- **State Management**: Provider pattern
- **Backend**: Supabase
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL (via Supabase)
- **Storage**: Supabase Storage

## 📚 Documentation

- [iOS TestFlight Deployment Workflow](./IOS_TESTFLIGHT_WORKFLOW.md)
- [Supabase Setup Guide](./SUPABASE_SETUP.md)
- [Onboarding Setup Guide](./ONBOARDING_SETUP_GUIDE.md)
- [Claude AI Integration](./CLAUDE.md)

## 🔧 Configuration

### iOS Deployment
- **Bundle ID**: `com.abenezernuro.agenticfit`
- **Team ID**: `HP284BJ924`
- **Target**: iOS 12.0+

### Environment Variables
Create a `.env` file with:
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🚀 Deployment

### iOS TestFlight
See [IOS_TESTFLIGHT_WORKFLOW.md](./IOS_TESTFLIGHT_WORKFLOW.md) for complete deployment instructions.

Quick deploy:
```bash
flutter clean
flutter pub get
cd ios && pod install && cd ..
export PATH="/opt/homebrew/bin:$PATH"
flutter build ipa --release
```

Then upload via Apple Transporter or Xcode Organizer.

## 🧪 Testing

```bash
# Unit tests
flutter test

# Widget tests
flutter test test/widget_test.dart

# Integration tests
flutter drive --target=test_driver/app.dart
```

## 🤝 Contributing

1. Follow the coding standards in `.cursorrules`
2. Write tests for new features
3. Update documentation as needed
4. Test on multiple platforms before submitting

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the documentation files
2. Review common issues in the workflow guides
3. Create an issue with detailed information

## 🔗 Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [Supabase Flutter Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-flutter)
- [Apple Developer Documentation](https://developer.apple.com/documentation/)
