# OpenFit v2 - Flutter Fitness App Cursor Rules

## Project Overview
OpenFit v2 is a Flutter-based fitness application with Supabase backend integration, designed for cross-platform deployment with a focus on iOS TestFlight distribution.

## Development Guidelines

### Flutter & Dart Standards
- Use Flutter 3.32+ with Dart 3.8+
- Follow official Flutter style guide and linting rules
- Prefer composition over inheritance
- Use const constructors where possible
- Implement proper null safety
- Use meaningful variable and function names

### Architecture Patterns
- Follow Provider pattern for state management
- Implement clean architecture with separation of concerns:
  - `/lib/models/` - Data models and entities
  - `/lib/services/` - Business logic and API calls
  - `/lib/providers/` - State management
  - `/lib/screens/` - UI screens and pages
  - `/lib/widgets/` - Reusable UI components
  - `/lib/components/` - Complex UI components
  - `/lib/utils/` - Helper functions and utilities
  - `/lib/config/` - App configuration

### Code Quality
- Write comprehensive unit tests in `/test/`
- Use meaningful commit messages
- Document complex functions and classes
- Handle errors gracefully with try-catch blocks
- Implement proper loading states and error handling
- Use async/await for asynchronous operations

### Supabase Integration
- Use environment variables for sensitive data
- Implement Row Level Security (RLS) policies
- Follow Supabase best practices for authentication
- Use proper error handling for database operations
- Implement offline-first approach where possible

### iOS Deployment
- Bundle ID: `com.abenezernuro.agenticfit`
- Team ID: `HP284BJ924`
- Target iOS 12.0+
- Use automatic code signing
- Follow Apple's App Store guidelines

### Package Management
- Always use package managers (flutter pub, pod install) instead of manual file editing
- Keep dependencies up to date
- Document any custom package modifications

### Security
- Never commit sensitive data (API keys, passwords)
- Use flutter_secure_storage for sensitive local data
- Implement proper authentication flows
- Validate all user inputs

### Performance
- Optimize images and assets
- Use lazy loading for lists
- Implement proper memory management
- Profile app performance regularly

### Testing
- Write unit tests for business logic
- Implement widget tests for UI components
- Test on multiple devices and screen sizes
- Test offline scenarios

## File Naming Conventions
- Use snake_case for file names
- Use PascalCase for class names
- Use camelCase for variables and functions
- Use SCREAMING_SNAKE_CASE for constants

## Git Workflow
- Use feature branches for new development
- Write descriptive commit messages
- Test thoroughly before merging
- Keep commits atomic and focused

## Common Commands
```bash
# Development
flutter pub get
flutter run
flutter test

# iOS Build & Deploy
flutter clean
flutter pub get
cd ios && pod install && cd ..
flutter build ios --release
flutter build ipa --release

# Code Quality
flutter analyze
dart format .
flutter test --coverage
```

## Troubleshooting
- Always run `flutter clean` when switching branches
- Use `pod install` after dependency changes
- Check bundle identifier matches App Store Connect
- Verify code signing settings in Xcode
- Test on physical devices before release

## Resources
- [Flutter Documentation](https://docs.flutter.dev/)
- [Supabase Flutter Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-flutter)
- [Apple Developer Documentation](https://developer.apple.com/documentation/)
