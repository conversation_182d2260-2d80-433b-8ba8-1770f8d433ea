# OpenFit Onboarding Data Prefill Setup Guide

## 🔧 **What I Fixed:**

The issue was that your onboarding screen wasn't loading existing user profile data to prefill the form fields. Here's what I implemented:

### **1. Async Profile Loading**
- Added a loading state while fetching existing profile data
- The onboarding screen now loads any existing profile data before showing the form
- Shows a loading spinner with "Loading your profile..." message

### **2. Form Field Updates**
- Fixed all onboarding steps to properly update when new data is loaded
- Added `didUpdateWidget` methods to all steps to handle dynamic data updates
- Text controllers and selection lists now properly reflect loaded data

### **3. Enhanced Debugging**
- Added debug logging to track data loading and conversion
- <PERSON>sol<PERSON> will show if profile data is found and what fields are populated

## 🚀 **Required Database Setup:**

**IMPORTANT**: You need to run this SQL migration in your Supabase dashboard first:

### **Step 1: Database Migration**

Go to your Supabase Dashboard → SQL Editor and run this migration:

```sql
-- Migration to add onboarding fields to profiles table

-- Add columns for personal information
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS name TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS gender TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS age INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS height DECIMAL(5,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS height_unit TEXT DEFAULT 'cm';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS weight DECIMAL(5,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS weight_unit TEXT DEFAULT 'kg';

-- Add columns for fitness goals
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS fitness_goal_primary TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS fitness_goals_array TEXT[] DEFAULT '{}';

-- Add columns for fitness experience
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS fitness_experience TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS cardio_fitness_level INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS weightlifting_fitness_level INTEGER;

-- Add columns for workout preferences
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS workout_frequency INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS workout_duration TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS workout_days TEXT[] DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS workout_environment TEXT[] DEFAULT '{}';

-- Add columns for equipment
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS equipment TEXT[] DEFAULT '{}';

-- Add columns for health and dietary information
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS health_conditions TEXT[] DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS physical_limitations TEXT[] DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS dietary_restrictions TEXT[] DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS additional_notes TEXT;

-- Ensure onboarding tracking columns exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS has_completed_preferences BOOLEAN DEFAULT FALSE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS fitness_assessment_completed BOOLEAN DEFAULT FALSE;

-- Add indexes for common queries
CREATE INDEX IF NOT EXISTS idx_profiles_onboarding_completed ON profiles(onboarding_completed);
CREATE INDEX IF NOT EXISTS idx_profiles_fitness_goal_primary ON profiles(fitness_goal_primary);
CREATE INDEX IF NOT EXISTS idx_profiles_fitness_experience ON profiles(fitness_experience);

-- Add RLS policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'profiles' AND policyname = 'Users can view own profile'
    ) THEN
        CREATE POLICY "Users can view own profile" ON profiles
            FOR SELECT USING (auth.uid() = id);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'profiles' AND policyname = 'Users can update own profile'
    ) THEN
        CREATE POLICY "Users can update own profile" ON profiles
            FOR UPDATE USING (auth.uid() = id);
    END IF;
END$$;
```

### **Step 2: Test the System**

1. **With Existing Data**: If you have an existing user account, add some test data to see prefilling:
   ```sql
   -- Example: Add test data for your user
   UPDATE profiles 
   SET name = 'Test User',
       age = 25,
       gender = 'Male',
       height = 175.5,
       weight = 70.0,
       fitness_goal_primary = 'Weight loss'
   WHERE id = 'your-user-id';
   ```

2. **Run your app** and navigate to the onboarding screen
3. **Check the console logs** for debugging information:
   - "Existing profile found: [list of fields]"
   - "Converted onboarding data - Name: ..., Age: ..."
   - Or "No existing profile found, creating new OnboardingData"

### **Step 3: Verify Prefilling Works**

1. **Open onboarding screen** - you should see a loading spinner first
2. **Form fields should be populated** with any existing data
3. **All steps should show existing selections** (goals, equipment, etc.)

## 📊 **How It Works:**

1. **OnboardingScreen loads** → Shows loading spinner
2. **ProfileService fetches** existing user data from Supabase
3. **Data is converted** to OnboardingData model
4. **All form steps update** to show existing data
5. **User can modify** and save changes

## 🐛 **Troubleshooting:**

### **No Data Showing:**
1. Check console logs for errors
2. Verify the database migration ran successfully
3. Ensure user is authenticated
4. Check if profile data exists in the database

### **Loading Forever:**
1. Check network connection
2. Verify Supabase credentials
3. Check browser console for API errors

### **Data Not Saving:**
1. Verify RLS policies are set up correctly
2. Check user permissions
3. Look for database constraint errors

## 🔍 **Debug Commands:**

Check your data in Supabase SQL Editor:

```sql
-- Check if columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'profiles';

-- Check your user's profile data
SELECT * FROM profiles WHERE id = auth.uid();

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'profiles';
```

## ✅ **Expected Behavior:**

- **First time users**: Empty form (normal onboarding flow)
- **Returning users**: Form prefilled with their existing data
- **Partial data**: Some fields filled, others empty
- **Complete data**: All fields populated from previous onboarding

The system is now fully set up to load and prefill existing user data in your onboarding flow!