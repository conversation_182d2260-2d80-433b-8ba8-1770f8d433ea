# Supabase OpenFit MCP Integration Setup

This guide explains how to set up your Supabase database to work with the OpenFit onboarding system and MCP integration.

## Database Migration

First, run the SQL migration on your Supabase database to add the necessary columns to the `profiles` table:

1. Go to your Supabase Dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase_migration_profiles.sql`
4. Execute the migration

This will add all the necessary columns to store onboarding data including:
- Personal information (name, age, gender, height, weight)
- Fitness goals and experience levels
- Workout preferences and schedule
- Equipment access
- Health conditions and dietary restrictions

## Code Changes Made

### 1. New ProfileService (`lib/services/profile_service.dart`)
- Dedicated service for handling profile and onboarding data
- Methods for saving/retrieving onboarding data
- Helper methods for getting specific user preferences
- Proper error handling and data conversion

### 2. Updated OnboardingScreen (`lib/screens/onboarding/onboarding_screen.dart`)
- Now uses `ProfileService` instead of `AuthService` for saving onboarding data
- Improved error handling
- Better separation of concerns

### 3. Updated Main App (`lib/main.dart`)
- Added `ProfileService` to the provider tree
- Updated `AuthWrapper` to use `ProfileService.hasCompletedOnboarding()`

## Using Supabase MCP

Once your database is set up, you can use the Supabase MCP to interact with your data:

### Example MCP Commands

```javascript
// List all projects
mcp_supabase_openfit_mcp_list_projects

// Get project details
mcp_supabase_openfit_mcp_get_project({ id: "your-project-id" })

// List tables
mcp_supabase_openfit_mcp_list_tables({ project_id: "your-project-id" })

// Execute SQL queries
mcp_supabase_openfit_mcp_execute_sql({
  project_id: "your-project-id",
  query: "SELECT * FROM profiles WHERE onboarding_completed = true"
})

// Apply migrations
mcp_supabase_openfit_mcp_apply_migration({
  project_id: "your-project-id",
  name: "add_profile_columns",
  query: "ALTER TABLE profiles ADD COLUMN fitness_level INTEGER;"
})
```

## Data Structure

### OnboardingData Fields Mapped to Database Columns

| Dart Field | Database Column | Type | Description |
|------------|----------------|------|-------------|
| `name` | `name` | TEXT | User's full name |
| `gender` | `gender` | TEXT | User's gender |
| `age` | `age` | INTEGER | User's age |
| `height` | `height` | DECIMAL(5,2) | Height value |
| `heightUnit` | `height_unit` | TEXT | 'cm' or 'ft' |
| `weight` | `weight` | DECIMAL(5,2) | Weight value |
| `weightUnit` | `weight_unit` | TEXT | 'kg' or 'lbs' |
| `primaryGoal` | `fitness_goal_primary` | TEXT | Main fitness goal |
| `fitnessGoals` | `fitness_goals_array` | TEXT[] | Array of goals |
| `fitnessExperience` | `fitness_experience` | TEXT | Experience level |
| `cardioLevel` | `cardio_fitness_level` | INTEGER | 1-10 scale |
| `weightliftingLevel` | `weightlifting_fitness_level` | INTEGER | 1-10 scale |
| `workoutFrequency` | `workout_frequency` | INTEGER | Times per week |
| `workoutDuration` | `workout_duration` | TEXT | Duration category |
| `workoutDays` | `workout_days` | TEXT[] | Preferred days |
| `workoutEnvironment` | `workout_environment` | TEXT[] | Where they workout |
| `equipment` | `equipment` | TEXT[] | Available equipment |
| `healthConditions` | `health_conditions` | TEXT[] | Medical conditions |
| `physicalLimitations` | `physical_limitations` | TEXT[] | Physical limitations |
| `dietaryRestrictions` | `dietary_restrictions` | TEXT[] | Dietary needs |
| `additionalNotes` | `additional_notes` | TEXT | Free text notes |

## ProfileService Usage Examples

### Save Onboarding Data
```dart
final profileService = context.read<ProfileService>();
await profileService.saveOnboardingData(onboardingData);
```

### Get User Profile
```dart
final profile = await profileService.getUserProfile();
```

### Check Onboarding Status
```dart
final completed = await profileService.hasCompletedOnboarding();
```

### Get User's Equipment
```dart
final equipment = await profileService.getUserEquipment();
```

### Get Workout Preferences
```dart
final preferences = await profileService.getWorkoutPreferences();
```

## Security Notes

The migration includes Row Level Security (RLS) policies:
- Users can only view their own profile
- Users can only update their own profile
- All data is properly isolated by user ID

## Next Steps

1. Run the database migration
2. Test the onboarding flow
3. Use the MCP commands to query and analyze user data
4. Build features like:
   - Personalized workout recommendations based on equipment and goals
   - Progress tracking
   - Goal-based challenges
   - Dietary recommendations

## Troubleshooting

### Common Issues

1. **Migration fails**: Check that you have proper permissions in Supabase
2. **ProfileService not found**: Ensure it's added to the provider tree in `main.dart`
3. **Data not saving**: Check RLS policies and user authentication
4. **MCP connection issues**: Verify your MCP server configuration

### Debug Queries

```sql
-- Check if columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'profiles';

-- Check user data
SELECT id, name, onboarding_completed, fitness_goal_primary 
FROM profiles 
WHERE id = auth.uid();

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'profiles';
```